<!-- template部分保持不变 -->
<template>
    <div class="drop-container" @dragover="handleDragOver" @drop="handleDrop">
        <div v-if="imageSrc" class="image-container">
            <img ref="imageRef" :src="imageSrc" :alt="imageAlt" class="base-image" @load="onImageLoad">
            <canvas ref="canvasRef" class="drawing-canvas" @mousedown="startDrawing" @mousemove="draw"
                @mouseup="stopDrawing"></canvas>
        </div>
        <div v-else class="drop-text">拖拽图片到这里</div>
        <button v-if="imageSrc" class="parse-btn" @click="parseImage">解析</button>
    </div>
</template>

<script setup>
    import { ref } from 'vue';

    // 响应式数据
    const imageSrc = ref('');
    const imageAlt = ref('');
    const imageRef = ref(null);
    const canvasRef = ref(null);
    const ctx = ref(null);
    const lines = ref([]); // 存储检测结果
    const handleDragOver = (event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
    };

    const handleDrop = (event) => {
        event.preventDefault();
        const file = event.dataTransfer.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                imageSrc.value = e.target.result;
                imageAlt.value = file.name;
            };
            reader.readAsDataURL(file);
        }
    };

    const onImageLoad = () => {
        const canvas = canvasRef.value;
        const image = imageRef.value;
        const imageContainer = image.parentElement; // 获取 image-container 父元素

        if (canvas && image && imageContainer) {
            // 设置 canvas 和 imageContainer 的尺寸为图片的实际尺寸
            canvas.width = image.naturalWidth;
            canvas.height = image.naturalHeight;

            // 设置 image-container 的尺寸
            imageContainer.style.width = `${image.naturalWidth}px`;
            imageContainer.style.height = `${image.naturalHeight}px`;

            ctx.value = canvas.getContext('2d');
        }
    };
    // 工具函数
    const calcLuminance = (pixels, index) =>
        0.299 * pixels[index] + 0.587 * pixels[index + 1] + 0.114 * pixels[index + 2];

    // 横线检测
    const detectHorizontalLines = (canvas, pixels) => {
        const detectedLines = [];

        for (let y = 0; y < canvas.height; y++) {
            let counter = 0;
            let startX = -1;

            for (let x = 0; x < canvas.width; x++) {
                const index = (y * canvas.width + x) * 4;

                if (calcLuminance(pixels, index) < 250) {
                    if (startX === -1) startX = x;
                    counter++;
                } else {
                    if (counter >= 100) {
                        detectedLines.push({
                            y,
                            x1: startX,
                            x2: x,
                            length: x - startX,
                            verticalLines: []
                        });
                    }
                    counter = 0;
                    startX = -1;
                }
            }

            // 处理行尾
            if (counter >= 100) {
                detectedLines.push({
                    y,
                    x1: startX,
                    x2: canvas.width,
                    length: canvas.width - startX,
                    verticalLines: []
                });
            }
        }

        return detectedLines.sort((a, b) => a.y - b.y);
    };

    // 竖线检测
    const detectVerticalLines = (lines, canvas, pixels) => {
        for (let i = 0, l = lines.length - 1; i < l; i++) {
            const current = lines[i];

            // 确定扫描范围
            const minY = current.y;
            const maxY = lines[l].y;

            // 逐列检测
            for (let x = current.x1; x < current.x2; x++) {
                let isValid = true;
                let li = i + 1; // 横线序号
                // 垂直扫描 
                for (let y = minY; y <= maxY; y++) {
                    const index = (y * canvas.width + x) * 4;
                    if (calcLuminance(pixels, index) < 250) {
                        if (lines[li].y == y) {
                            if (lines[li].x1 <= x) {
                                break;
                            } else {
                                li++;
                            }
                        }
                    } else {
                        isValid = false;
                        break;
                    }
                }
                // 有效竖线
                if (isValid) {
                    current.verticalLines.push({
                        x: x,
                        y1: minY,
                        y2: lines[li].y,
                        length: lines[li].y - minY,
                        pixes: [],
                        text: ''
                    });
                }
            }
            current.verticalLines = mergeCloseLines(current.verticalLines);

            for (let v = 0, vl = current.verticalLines.length - 1; v < vl; v++) {
                let l1 = current.verticalLines[v];
                let l2 = current.verticalLines[v + 1]
                const columnPixels = getPixelRect(pixels, l1.x + 2, l1.y1 + 2, l2.x - 2, l1.y2 - 2);
                l1.pixes = columnPixels.pixels;

            }
        }



    };

    function getPixelRect(imageData, x1, y1, x2, y2) {
        // 参数校验
        if (x1 > x2) [x1, x2] = [x2, x1];
        if (y1 > y2) [y1, y2] = [y2, y1];

        // 边界约束
        const clamp = (val, min, max) => Math.max(min, Math.min(max, val));
        x1 = clamp(x1, 0, imageData.width - 1);
        x2 = clamp(x2, 0, imageData.width - 1);
        y1 = clamp(y1, 0, imageData.height - 1);
        y2 = clamp(y2, 0, imageData.height - 1);

        const rectWidth = x2 - x1 + 1;
        const rectHeight = y2 - y1 + 1;
        const pixels = new Array(rectHeight);

        // 预计算参数
        const stride = imageData.width * 4;
        const startX = x1 * 4;
        const endX = (x2 + 1) * 4;

        // 逐行提取
        for (let y = y1; y <= y2; y++) {
            const rowIndex = y - y1;
            const rowData = new Uint8ClampedArray(rectWidth * 4);

            // 获取整行数据
            const startIndex = y * stride + startX;
            const endIndex = y * stride + endX;
            rowData.set(imageData.data.subarray(startIndex, endIndex));

            // 重组为像素数组
            pixels[rowIndex] = [];
            for (let x = 0; x < rowData.length; x += 4) {
                pixels[rowIndex].push([
                    rowData[x],     // R
                    rowData[x + 1],   // G
                    rowData[x + 2],   // B
                    rowData[x + 3]    // A
                ]);
            }
        }

        return {
            width: rectWidth,
            height: rectHeight,
            pixels: pixels
        };
    }

    const mergeCloseLines = (sorted) => {
        if (sorted.length === 0) return [];
        const result = [];
        let currentMaxItem = sorted[0];  // 当前组中length最大的元素
        let currentMaxX = sorted[0].x;   // 当前组的最大x坐标

        for (let i = 1; i < sorted.length; i++) {
            const item = sorted[i];

            // 2. 判断是否属于当前组（x差值在阈值内）
            if (Math.abs(item.x - currentMaxX) <= 5) { // 5像素以内的竖线当一条处理
                // 3. 更新当前组的代表元素：优先length更大，length相同取x更大的
                if (item.length > currentMaxItem.length ||
                    (item.length === currentMaxItem.length && item.x > currentMaxItem.x)) {
                    currentMaxItem = item;
                    currentMaxX = item.x;
                }
            } else {
                // 4. 遇到新组，保存旧组结果并初始化新组
                result.push(currentMaxItem);
                currentMaxItem = item;
                currentMaxX = item.x;
            }
        }

        // 5. 添加最后一个组的处理结果
        result.push(currentMaxItem);

        return result;
    }
    // 主解析函数
    const parseImage = async () => {
        if (!imageRef.value) return;

        // 准备离屏Canvas
        const offscreenCanvas = document.createElement('canvas');
        offscreenCanvas.width = imageRef.value.naturalWidth;
        offscreenCanvas.height = imageRef.value.naturalHeight;
        const ctx = offscreenCanvas.getContext('2d');

        // 绘制原始图像
        ctx.drawImage(imageRef.value, 0, 0);
        const imageData = ctx.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height);

        // 检测横线
        let detectedLines = detectHorizontalLines(offscreenCanvas, imageData.data);

        // 过滤相邻横线（垂直间距<2像素）
        detectedLines = detectedLines.filter((line, index, arr) =>
            index === 0 || line.y - arr[index - 1].y >= 2
        );

        // 检测竖线
        detectVerticalLines(detectedLines, offscreenCanvas, imageData.data);

        // 更新响应式数据
        lines.value = detectedLines;
        console.log(lines.value)
        // 可视化渲染
        renderResults(offscreenCanvas, imageData);
    };

    // 渲染函数
    const renderResults = (offscreenCanvas, imageData) => {
        const ctx = offscreenCanvas.getContext('2d');

        // 绘制横线（红色）
        lines.value.forEach(line => {
            for (let x = line.x1; x < line.x2; x++) {
                const index = (line.y * offscreenCanvas.width + x) * 4;
                imageData.data[index] = 255;     // R
                imageData.data[index + 1] = 0;   // G
                imageData.data[index + 2] = 0;   // B
            }
        });

        // 绘制竖线（绿色）
        lines.value.forEach(line => {
            line.verticalLines.forEach(vLine => {
                for (let y = vLine.y1; y < vLine.y2; y++) {
                    const index = (y * offscreenCanvas.width + vLine.x) * 4;
                    imageData.data[index] = 0;     // R
                    imageData.data[index + 1] = 255; // G
                    imageData.data[index + 2] = 0;   // B
                }
            });
        });

        // 更新Canvas
        ctx.putImageData(imageData, 0, 0);
        const mainCtx = canvasRef.value.getContext('2d');
        mainCtx.drawImage(offscreenCanvas, 0, 0);
    };


</script>

<style>
    #app {
        display: flex;
        justify-content: center;
    }

    .drop-container {
        display: flex;
        justify-content: center;
        /* 水平居中 */
        border: 2px dashed #999;
        border-radius: 4px;
        padding: 20px;
        min-height: 200px;
        /* text-align: center; */
        position: relative;
    }

    /* 解析按钮样式 */
    .parse-btn {
        position: absolute;
        top: 10px;
        /* 距离顶部10px */
        right: 10px;
        /* 距离右侧10px */
        z-index: 3;
        /* 确保在图片和画布之上 */
        background-color: #4CAF50;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }

    .parse-btn:hover {
        background-color: #45a049;
    }

    .image-container {
        position: relative;
        display: inline-block;
    }

    .base-image {
        display: block;
        width: 100%;
        height: auto;
        opacity: 0.5;
        position: absolute;
        top: 0;
        left: 0;
    }

    .drawing-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: transparent;
        cursor: crosshair;
        z-index: 1;
    }

    .drop-text {
        font-size: 1.2em;
        color: #666;
        padding: 10px;
    }
</style>