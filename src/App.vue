<!-- template部分保持不变 -->
<template>
    <div class="drop-container" @dragover="handleDragOver" @drop="handleDrop">
        <div v-if="imageSrc" class="image-container">
            <img ref="imageRef" :src="imageSrc" :alt="imageAlt" class="base-image" @load="onImageLoad">
            <canvas ref="canvasRef" class="drawing-canvas" @mousedown="startDrawing" @mousemove="draw"
                @mouseup="stopDrawing"></canvas>
            <!-- 文字覆盖层 -->
            <div v-for="(overlay, index) in textOverlays" v-show="showTextOverlays" :key="index"
                class="text-overlay"
                :class="{ 'editing': editingOverlay === index }"
                :style="{
                    left: overlay.x + 'px',
                    top: overlay.y + 'px',
                    width: overlay.width + 'px',
                    height: overlay.height + 'px'
                }"
                @dblclick="startEditOverlay(index)">
                <!-- 编辑状态：显示输入框 -->
                <input
                    v-if="editingOverlay === index"
                    v-model="editingText"
                    class="text-input"
                    :style="{
                        width: '100%',
                        height: '100%'
                    }"
                    @keydown="handleKeydown"
                    @blur="finishEditOverlay"
                />
                <!-- 非编辑状态：显示文字 -->
                <span v-else>{{ overlay.text }}</span>
            </div>
        </div>
        <div v-else class="drop-text">拖拽图片到这里</div>
        <button v-if="imageSrc" class="parse-btn" @click="parseImage" :disabled="isProcessing">
            {{ isProcessing ? '处理中...' : '解析' }}
        </button>
        <button v-if="textOverlays.length > 0" class="toggle-text-btn" @click="toggleTextDisplay">
            {{ showTextOverlays ? '隐藏文字' : '显示文字' }}
        </button>
        <button v-if="textOverlays.length > 0" class="create-json-btn" @click="createJson">
            生成JSON数据
        </button>
        <!-- <button v-if="textOverlays.length > 0" class="verify-sync-btn" @click="verifyDataSync">
            验证数据同步
        </button> -->
        <div v-if="ocrProgress.show" class="ocr-progress">
            OCR 识别进度: {{ ocrProgress.current }}/{{ ocrProgress.total }}
        </div>
    </div>
</template>

<script setup>
    import { ref } from 'vue';
    import { createWorker } from 'tesseract.js';

    // 响应式数据
    const imageSrc = ref('');
    const imageAlt = ref('');
    const imageRef = ref(null);
    const canvasRef = ref(null);
    const ctx = ref(null);
    const lines = ref([]); // 存储检测结果
    const isProcessing = ref(false);
    const ocrProgress = ref({
        show: false,
        current: 0,
        total: 0
    });
    const textOverlays = ref([]); // 存储文字覆盖层信息
    const showTextOverlays = ref(true); // 控制文字覆盖层显示/隐藏
    const editingOverlay = ref(null); // 当前正在编辑的覆盖层索引
    const editingText = ref(''); // 编辑中的文字内容

    // Tesseract worker
    let tesseractWorker = null;

    // 切换文字显示
    const toggleTextDisplay = () => {
        showTextOverlays.value = !showTextOverlays.value;
    };

    // 开始编辑文字覆盖层
    const startEditOverlay = async (index) => {
        editingOverlay.value = index;
        editingText.value = textOverlays.value[index].text;

        // 等待 DOM 更新后聚焦输入框
        const inputElement = document.querySelector('.text-input');
        if (inputElement) {
            inputElement.focus();
            inputElement.select(); // 选中所有文字
        }
    };

    // 完成编辑（回车提交）
    const finishEditOverlay = () => {
        if (editingOverlay.value !== null) {
            const overlay = textOverlays.value[editingOverlay.value];
            const newText = editingText.value.trim();

            // 更新覆盖层文字
            overlay.text = newText;

            // 同步更新原始数据结构中的文字
            if (overlay.originalTextRef) {
                overlay.originalTextRef.text = newText;
                console.log(`已同步更新原始数据: lineIndex=${overlay.lineIndex}, verticalLineIndex=${overlay.verticalLineIndex}, text="${newText}"`);
            }

            editingOverlay.value = null;
            editingText.value = '';
        }
    };

    // 取消编辑（ESC键）
    const cancelEditOverlay = () => {
        if (editingOverlay.value !== null) {
            console.log('取消编辑，恢复原始文字');
        }
        editingOverlay.value = null;
        editingText.value = '';
    };

    // 处理键盘事件
    const handleKeydown = (event) => {
        if (editingOverlay.value !== null) {
            if (event.key === 'Enter') {
                event.preventDefault();
                finishEditOverlay();
            } else if (event.key === 'Escape') {
                event.preventDefault();
                cancelEditOverlay();
            }
        }
    };

    // 验证数据同步状态（调试用）
    // const verifyDataSync = () => {
    //     console.log('=== 数据同步验证 ===');
    //     textOverlays.value.forEach((overlay, index) => {
    //         if (overlay.originalTextRef) {
    //             const overlayText = overlay.text;
    //             const originalText = overlay.originalTextRef.text;
    //             const isSync = overlayText === originalText;
    //             console.log(`覆盖层${index}: 覆盖层文字="${overlayText}", 原始文字="${originalText}", 同步状态=${isSync}`);
    //         }
    //     });
    //     console.log('=== lines 数据结构 ===');
    //     console.log(lines.value);
    // };

    const handleDragOver = (event) => {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
    };

    const handleDrop = (event) => {
        event.preventDefault();
        const file = event.dataTransfer.files[0];
        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                imageSrc.value = e.target.result;
                imageAlt.value = file.name;
            };
            reader.readAsDataURL(file);
        }
    };

    const onImageLoad = () => {
        const canvas = canvasRef.value;
        const image = imageRef.value;
        const imageContainer = image.parentElement; // 获取 image-container 父元素

        if (canvas && image && imageContainer) {
            // 设置 canvas 和 imageContainer 的尺寸为图片的实际尺寸
            canvas.width = image.naturalWidth;
            canvas.height = image.naturalHeight;

            // 设置 image-container 的尺寸
            imageContainer.style.width = `${image.naturalWidth}px`;
            imageContainer.style.height = `${image.naturalHeight}px`;

            ctx.value = canvas.getContext('2d');
        }
    };
    // 初始化 Tesseract worker
    const initTesseractWorker = async () => {
        if (!tesseractWorker) {
            tesseractWorker = await createWorker('chi_sim+eng', 1
                // , {logger: m => console.log(m)}
            );
            await tesseractWorker.setParameters({ preserve_interword_spaces: '1' })

            // await tesseractWorker.setParameters({
            //     tessedit_char_whitelist: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789()、',
            // });
        }
        return tesseractWorker;
    };

    // 将像素数组转换为 Canvas ImageData
    const pixelsToImageData = (pixels, width, height) => {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        const imageData = ctx.createImageData(width, height);
        const data = imageData.data;

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const pixelIndex = y * width + x;
                const dataIndex = pixelIndex * 4;

                if (pixels[y] && pixels[y][x]) {
                    data[dataIndex] = pixels[y][x][0];     // R
                    data[dataIndex + 1] = pixels[y][x][1]; // G
                    data[dataIndex + 2] = pixels[y][x][2]; // B
                    data[dataIndex + 3] = pixels[y][x][3]; // A
                } else {
                    // 默认白色背景
                    data[dataIndex] = 255;
                    data[dataIndex + 1] = 255;
                    data[dataIndex + 2] = 255;
                    data[dataIndex + 3] = 255;
                }
            }
        }

        ctx.putImageData(imageData, 0, 0);
        return canvas;
    };

    // OCR 识别函数
    const recognizeText = async (pixels, width, height) => {
        try {
            const worker = await initTesseractWorker();

            const canvas = pixelsToImageData(pixels, width, height);

            const { data: { text } } = await worker.recognize(canvas);
            return text.trim();
        } catch (error) {
            console.error('OCR 识别失败:', error);
            return '';
        }
    };

    // 工具函数
    const calcLuminance = (pixels, index) =>
        0.299 * pixels[index] + 0.587 * pixels[index + 1] + 0.114 * pixels[index + 2];

    // 横线检测
    const detectHorizontalLines = (canvas, pixels) => {
        const detectedLines = [];

        for (let y = 0; y < canvas.height; y++) {
            let counter = 0;
            let startX = -1;

            for (let x = 0; x < canvas.width; x++) {
                const index = (y * canvas.width + x) * 4;

                if (calcLuminance(pixels, index) < 250) {
                    if (startX === -1) startX = x;
                    counter++;
                } else {
                    if (counter >= 100) {
                        detectedLines.push({
                            y,
                            x1: startX,
                            x2: x,
                            length: x - startX,
                            verticalLines: []
                        });
                    }
                    counter = 0;
                    startX = -1;
                }
            }

            // 处理行尾
            if (counter >= 100) {
                detectedLines.push({
                    y,
                    x1: startX,
                    x2: canvas.width,
                    length: canvas.width - startX,
                    verticalLines: []
                });
            }
        }

        return detectedLines.sort((a, b) => a.y - b.y);
    };

    // 竖线检测
    const detectVerticalLines = async (lines, canvas, imageData) => {
        for (let i = 0, l = lines.length - 1; i < l; i++) {
            const current = lines[i];

            // 确定扫描范围
            const minY = current.y;
            const maxY = lines[l].y;

            // 逐列检测
            for (let x = current.x1; x < current.x2; x++) {
                let isValid = true;
                let li = i + 1; // 横线序号
                // 垂直扫描
                for (let y = minY; y <= maxY; y++) {
                    const index = (y * canvas.width + x) * 4;
                    if (calcLuminance(imageData.data, index) < 250) {
                        if (lines[li].y == y) {
                            if (lines[li].x1 <= x) {
                                break;
                            } else {
                                li++;
                            }
                        }
                    } else {
                        isValid = false;
                        break;
                    }
                }
                // 有效竖线
                if (isValid) {
                    current.verticalLines.push({
                        x: x,
                        y1: minY,
                        y2: lines[li].y,
                        length: lines[li].y - minY,
                        pixes: [],
                        text: ''
                    });
                }
            }
            current.verticalLines = mergeCloseLines(current.verticalLines);

            // 设置 OCR 进度
            const totalColumns = current.verticalLines.length - 1;
            if (totalColumns > 0) {
                ocrProgress.value.show = true;
                ocrProgress.value.total = totalColumns;
                ocrProgress.value.current = 0;
                ocrProgress.value.skipped = 0;
            }

            // 处理像素提取和 OCR 识别
            for (let v = 0, vl = current.verticalLines.length - 1; v < vl; v++) {
                let l1 = current.verticalLines[v];
                let l2 = current.verticalLines[v + 1];

                // 使用原始图像数据提取像素
                const columnPixels = getPixelRect(
                    imageData,
                    l1.x + 2, l1.y1 + 2, l2.x - 2, l1.y2 - 2
                );
                l1.pixes = columnPixels.pixels;

                // 调试信息：检查提取的像素是否正确
                if (columnPixels.pixels.length > 0 && columnPixels.pixels[0].length > 0) {
                    // const firstPixel = columnPixels.pixels[0][0];
                    // console.log(`提取区域 (${l1.x + 2}, ${l1.y1 + 2}) 到 (${l2.x - 2}, ${l1.y2 - 2})`);
                    // console.log(`区域尺寸: ${columnPixels.width}x${columnPixels.height}`);
                    // console.log(`第一个像素值: R=${firstPixel[0]}, G=${firstPixel[1]}, B=${firstPixel[2]}, A=${firstPixel[3]}`);
                    // console.log(`是否同色块: ${columnPixels.isSameColor}`);
                    // if (columnPixels.isSameColor && columnPixels.dominantColor) {
                    //     // const [r, g, b] = columnPixels.dominantColor;
                    //     // console.log(`主要颜色: RGB(${r}, ${g}, ${b})`);
                    // }

                    // 快速检测：如果是同色块，直接判断是否值得 OCR
                    let worthOCR = false;
                    if (columnPixels.isSameColor) {
                        // 同色块的判断逻辑
                        if (columnPixels.dominantColor) {
                            const [r, g, b] = columnPixels.dominantColor;
                            const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                            // 如果是深色同色块，可能是填充的文字区域，值得识别
                            // 如果是浅色同色块，可能是空白区域，跳过
                            worthOCR = luminance < 200 && (columnPixels.width >= 15 && columnPixels.height >= 10);
                            // console.log(`同色块亮度: ${luminance.toFixed(1)}, 值得OCR: ${worthOCR}`);
                        }
                    }
                    else {
                        // 非同色块，使用原有的复杂检测逻辑
                        worthOCR = true;
                        // isPixelRegionWorthOCR(
                        //     columnPixels.pixels,
                        //     columnPixels.width,
                        //     columnPixels.height
                        // );
                    }

                    if (worthOCR) {
                        // 进行 OCR 识别
                        try {
                            // console.log(`开始 OCR 识别区域 ${v + 1}...`);
                            const recognizedText = await recognizeText(
                                columnPixels.pixels,
                                columnPixels.width,
                                columnPixels.height
                            );
                            l1.text = recognizedText;

                            console.log(recognizedText);

                            // 如果识别出文字，创建文字覆盖层
                            if (recognizedText.trim()) {
                                textOverlays.value.push({
                                    x: l1.x + 2,
                                    y: l1.y1 + 2,
                                    width: (l2.x - 2) - (l1.x + 2),
                                    height: (l1.y2 - 2) - (l1.y1 + 2),
                                    text: recognizedText.trim(),
                                    // 添加原始数据引用，用于同步
                                    lineIndex: i,
                                    verticalLineIndex: v,
                                    originalTextRef: l1 // 直接引用原始对象
                                });
                            }
                        } catch (error) {
                            console.error(`OCR 识别失败:`, error);
                            l1.text = '';
                        }


                    } else {
                        // console.log(`跳过 OCR 识别区域 ${v + 1} - 同色块或内容不足`);
                        l1.text = ''; // 设置为空文本
                        ocrProgress.value.skipped++; // 增加跳过计数
                    }
                    l1.pixes = null;
                    // 更新进度
                    ocrProgress.value.current = v + 1;
                }
            }
        }



    };
    const createJson = () => {
        console.log(lines.value.slice(0, -1));
    }
    function getPixelRect(imageData, x1, y1, x2, y2) {
        // 参数校验和类型检查
        if (!imageData || !imageData.data || !imageData.width || !imageData.height) {
            console.error('Invalid imageData object');
            return { width: 0, height: 0, pixels: [] };
        }

        // 坐标校正
        if (x1 > x2) [x1, x2] = [x2, x1];
        if (y1 > y2) [y1, y2] = [y2, y1];

        // 边界约束
        const clamp = (val, min, max) => Math.max(min, Math.min(max, val));
        x1 = clamp(Math.floor(x1), 0, imageData.width - 1);
        x2 = clamp(Math.floor(x2), 0, imageData.width - 1);
        y1 = clamp(Math.floor(y1), 0, imageData.height - 1);
        y2 = clamp(Math.floor(y2), 0, imageData.height - 1);

        const rectWidth = x2 - x1 + 1;
        const rectHeight = y2 - y1 + 1;

        // 检查有效区域
        if (rectWidth <= 0 || rectHeight <= 0) {
            console.warn('Invalid rectangle dimensions');
            return { width: 0, height: 0, pixels: [] };
        }

        const pixels = new Array(rectHeight);

        // 预计算参数
        const stride = imageData.width * 4;

        // 同色检测变量
        let firstPixelColor = null;
        let isSameColor = true;
        const colorTolerance = 10; // 颜色容差，允许轻微的颜色差异

        // 逐行提取
        for (let y = y1; y <= y2; y++) {
            const rowIndex = y - y1;
            pixels[rowIndex] = [];

            // 逐像素提取
            for (let x = x1; x <= x2; x++) {
                const index = y * stride + x * 4;
                const currentPixel = [
                    imageData.data[index],     // R
                    imageData.data[index + 1], // G
                    imageData.data[index + 2], // B
                    imageData.data[index + 3]  // A
                ];

                pixels[rowIndex].push(currentPixel);

                // 同色检测：比较当前像素与第一个像素
                if (firstPixelColor === null) {
                    firstPixelColor = [...currentPixel];
                } else if (isSameColor) {
                    // 检查颜色差异（忽略透明度）
                    const rDiff = Math.abs(currentPixel[0] - firstPixelColor[0]);
                    const gDiff = Math.abs(currentPixel[1] - firstPixelColor[1]);
                    const bDiff = Math.abs(currentPixel[2] - firstPixelColor[2]);

                    if (rDiff > colorTolerance || gDiff > colorTolerance || bDiff > colorTolerance) {
                        isSameColor = false;
                    }
                }
            }
        }

        return {
            width: rectWidth,
            height: rectHeight,
            pixels: pixels,
            isSameColor: isSameColor, // 新增：是否为同色块
            dominantColor: firstPixelColor // 新增：主要颜色
        };
    }

    const mergeCloseLines = (sorted) => {
        if (sorted.length === 0) return [];
        const result = [];
        let currentMaxItem = sorted[0];  // 当前组中length最大的元素
        let currentMaxX = sorted[0].x;   // 当前组的最大x坐标

        for (let i = 1; i < sorted.length; i++) {
            const item = sorted[i];

            // 2. 判断是否属于当前组（x差值在阈值内）
            if (Math.abs(item.x - currentMaxX) <= 5) { // 5像素以内的竖线当一条处理
                // 3. 更新当前组的代表元素：优先length更大，length相同取x更大的
                if (item.length > currentMaxItem.length ||
                    (item.length === currentMaxItem.length && item.x > currentMaxItem.x)) {
                    currentMaxItem = item;
                    currentMaxX = item.x;
                }
            } else {
                // 4. 遇到新组，保存旧组结果并初始化新组
                result.push(currentMaxItem);
                currentMaxItem = item;
                currentMaxX = item.x;
            }
        }

        // 5. 添加最后一个组的处理结果
        result.push(currentMaxItem);

        return result;
    }
    // 主解析函数
    const parseImage = async () => {
        if (!imageRef.value || isProcessing.value) return;

        isProcessing.value = true;
        ocrProgress.value.show = false;
        ocrProgress.value.skipped = 0; // 重置跳过计数
        textOverlays.value = []; // 清空之前的文字覆盖层
        editingOverlay.value = null; // 清空编辑状态
        editingText.value = '';

        // 准备离屏Canvas
        const offscreenCanvas = document.createElement('canvas');
        offscreenCanvas.width = imageRef.value.naturalWidth;
        offscreenCanvas.height = imageRef.value.naturalHeight;
        const ctx = offscreenCanvas.getContext('2d');

        // 绘制原始图像
        ctx.drawImage(imageRef.value, 0, 0);
        const imageData = ctx.getImageData(0, 0, offscreenCanvas.width, offscreenCanvas.height);

        // 创建原始数据的副本，用于像素提取
        const originalImageData = {
            data: new Uint8ClampedArray(imageData.data),
            width: imageData.width,
            height: imageData.height
        };

        // 检测横线
        let detectedLines = detectHorizontalLines(offscreenCanvas, originalImageData.data);

        // 过滤相邻横线（垂直间距<2像素）
        detectedLines = detectedLines.filter((line, index, arr) =>
            index === 0 || line.y - arr[index - 1].y >= 2
        );

        // 检测竖线 - 使用原始数据
        await detectVerticalLines(detectedLines, offscreenCanvas, originalImageData);

        // 更新响应式数据
        lines.value = detectedLines;
        console.log(lines.value)
        // 可视化渲染 - 使用可修改的副本
        renderResults(offscreenCanvas, imageData);

        // 重置状态
        isProcessing.value = false;
        ocrProgress.value.show = false;
        showTextOverlays.value = true; // 默认显示文字

        //

    };

    // 渲染函数
    const renderResults = (offscreenCanvas, imageData) => {
        const ctx = offscreenCanvas.getContext('2d');

        // 绘制横线（红色）
        lines.value.forEach(line => {
            for (let x = line.x1; x < line.x2; x++) {
                const index = (line.y * offscreenCanvas.width + x) * 4;
                imageData.data[index] = 255;     // R
                imageData.data[index + 1] = 0;   // G
                imageData.data[index + 2] = 0;   // B
            }
        });

        // 绘制竖线（绿色）
        lines.value.forEach(line => {
            line.verticalLines.forEach(vLine => {
                for (let y = vLine.y1; y < vLine.y2; y++) {
                    const index = (y * offscreenCanvas.width + vLine.x) * 4;
                    imageData.data[index] = 0;     // R
                    imageData.data[index + 1] = 255; // G
                    imageData.data[index + 2] = 0;   // B
                }
            });
        });

        // 更新Canvas
        ctx.putImageData(imageData, 0, 0);
        const mainCtx = canvasRef.value.getContext('2d');
        mainCtx.drawImage(offscreenCanvas, 0, 0);
    };


</script>

<style>
    #app {
        display: flex;
        justify-content: center;
    }

    .drop-container {
        display: flex;
        justify-content: center;
        /* 水平居中 */
        border: 2px dashed #999;
        border-radius: 4px;
        padding: 20px;
        min-height: 200px;
        /* text-align: center; */
        position: relative;
    }

    /* 解析按钮样式 */
    .parse-btn {
        position: absolute;
        top: 10px;
        /* 距离顶部10px */
        right: 10px;
        /* 距离右侧10px */
        z-index: 3;
        /* 确保在图片和画布之上 */
        background-color: #4CAF50;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }

    .parse-btn:hover {
        background-color: #45a049;
    }

    .parse-btn:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

    .toggle-text-btn {
        position: absolute;
        top: 10px;
        right: 80px;
        /* 在解析按钮左侧 */
        z-index: 3;
        background-color: #007bff;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }

    .toggle-text-btn:hover {
        background-color: #0056b3;
    }

    .verify-sync-btn {
        position: absolute;
        top: 10px;
        right: 200px;
        z-index: 3;
        background-color: #6c757d;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }

    .verify-sync-btn:hover {
        background-color: #5a6268;
    }

    .create-json-btn {
        position: absolute;
        top: 10px;
        right: 178px;
        /* 在解析按钮左侧 */
        z-index: 3;
        background-color: #e4db2a;
        color: white;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }

    .create-json-btn:hover {
        background-color: #cec131;
    }

    .ocr-progress {
        position: absolute;
        top: 50px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 4;
    }

    .image-container {
        position: relative;
        display: inline-block;
    }

    .base-image {
        display: block;
        width: 100%;
        height: auto;
        opacity: 0.5;
        position: absolute;
        top: 0;
        left: 0;
    }

    .drawing-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: transparent;
        cursor: crosshair;
        z-index: 1;
    }

    .text-overlay {
        position: absolute;
        background-color: rgba(255, 255, 255, 0.95);
        border: 1px solid #007bff;
        border-radius: 3px;
        padding: 2px 4px;
        font-size: 11px;
        font-family: Arial, sans-serif;
        color: #333;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        word-wrap: break-word;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        line-height: 1.2;
        min-height: 16px;
        box-sizing: border-box;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .text-overlay:hover {
        background-color: rgba(255, 255, 255, 1);
        border-color: #0056b3;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    }

    .text-overlay.editing {
        background-color: rgba(255, 255, 255, 1);
        border: 2px solid #28a745;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
        padding: 1px 3px; /* 调整padding以适应更粗的边框 */
    }

    .text-input {
        border: none;
        outline: none;
        background: transparent;
        font-size: 11px;
        font-family: Arial, sans-serif;
        color: #333;
        text-align: center;
        padding: 0;
        margin: 0;
        box-sizing: border-box;
        resize: none;
    }

    .drop-text {
        font-size: 1.2em;
        color: #666;
        padding: 10px;
    }
</style>